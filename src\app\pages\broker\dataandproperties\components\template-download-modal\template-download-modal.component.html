<!-- <PERSON>dal Backdrop -->
<div class="modal-backdrop fade show" (click)="close.emit()"></div>

<!-- Modal Container -->
<div class="modal fade show d-block" tabindex="-1" role="dialog"
  [class.rtl-layout]="translationService.getCurrentLanguage() === 'ar'">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">

      <!-- Modal Header -->
      <div class="modal-header">
        <h4 class="modal-title">{{ getTranslatedText('TITLE') }}</h4>
        <button type="button" class="btn-close" (click)="close.emit()" aria-label="Close"></button>
      </div>

      <!-- Modal Body -->
      <div class="modal-body">
        <form>

          <!-- Compound Type -->
          <div class="mb-3">
            <label class="form-label">{{ getTranslatedText('COMPOUND_TYPE') }}</label>
            <select class="form-select" [(ngModel)]="compoundType" name="compoundType">
              <option value="" disabled>{{ getTranslatedText('SELECT_COMPOUND_TYPE') }}</option>
              <option *ngFor="let option of compoundTypeOptions" [value]="option.value">
                {{ getTranslatedText(option.key) }}
              </option>
            </select>
          </div>

          <!-- Operation Type -->
          <div class="mb-3">
            <label class="form-label">{{ getTranslatedText('OPERATION_TYPE') }}</label>
            <select class="form-select" [(ngModel)]="operationType" name="operationType">
              <option value="" disabled>{{ getTranslatedText('SELECT_OPERATION_TYPE') }}</option>
              <option *ngFor="let option of operationTypeOptions" [value]="option.value">
                {{ getTranslatedText(option.key) }}
              </option>
            </select>
          </div>

          <!-- Unit Type -->
          <div class="mb-3">
            <label class="form-label">{{ getTranslatedText('UNIT_TYPE') }}</label>
            <select class="form-select" [(ngModel)]="unitType" name="unitType">
              <option value="" disabled>{{ getTranslatedText('SELECT_UNIT_TYPE') }}</option>
              <option *ngFor="let option of unitTypeOptions" [value]="option.value">
                {{ getTranslatedUnitType(option.key) }}
              </option>
            </select>
          </div>

        </form>
      </div>

      <!-- Modal Footer -->
      <div class="modal-footer gap-2">
        <button type="button" class="btn btn-secondary" (click)="close.emit()" [disabled]="isDownloading">
          {{ getTranslatedText('CANCEL') }}
        </button>
        <button type="button" class="btn btn-primary" (click)="submit()"
          [disabled]="!unitType || !compoundType || !operationType || isDownloading">
          <span *ngIf="!isDownloading">{{ getTranslatedText('DOWNLOAD') }}</span>
          <span *ngIf="isDownloading">
            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
            {{ getTranslatedText('DOWNLOADING') }}
          </span>
        </button>
      </div>

    </div>
  </div>
</div>