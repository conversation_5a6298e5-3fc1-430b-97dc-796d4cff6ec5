import {
  ChangeDetectorRef,
  Component,
  OnInit,
  ViewChild,
  ElementRef,
} from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { UnitService } from '../services/unit.service';
import { saveAs } from 'file-saver';
import Swal from 'sweetalert2';
import { TranslationService } from 'src/app/modules/i18n';
import { PropertyTranslationService } from '../../../shared/services/property-translation.service';

@Component({
  selector: 'app-dataandproperties',
  templateUrl: './dataandproperties.component.html',
  styleUrl: './dataandproperties.component.scss',
})
export class DataandpropertiesComponent implements OnInit {
  @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>;

  showEmptyCard = false;
  showSuccessCard = false;
  showPublishCard = false;
  isFilterDropdownVisible = false;
  showDownloadModal = false;

  brokerId: any;
  user: any;

  properties: any[] = [];
  isLoading = false;
  appliedFilters: any = {};
  searchText: string = '';
  private searchTimeout: any;

  // Filter properties
  currentCompoundType: string = '';
  currentUnitType: string = '';
  activeFilterButtons: string[] = [];

  // Unit types for different compound types
  get outsideCompoundUnitTypes() {
    return [
      // Residential
      { key: this.getTranslatedUnitType('apartments'), value: 'apartments' },
      { key: this.getTranslatedUnitType('duplexes'), value: 'duplexes' },
      { key: this.getTranslatedUnitType('studios'), value: 'studios' },
      { key: this.getTranslatedUnitType('penthouses'), value: 'penthouses' },
      { key: this.getTranslatedUnitType('basement'), value: 'basement' },
      { key: this.getTranslatedUnitType('roofs'), value: 'roofs' },
      // Villas
      { key: this.getTranslatedUnitType('villa'), value: 'villas' },
      { key: this.getTranslatedUnitType('full_buildings'), value: 'full_buildings' },
      // Commercial/Administrative
      { key: this.getTranslatedUnitType('administrative_units'), value: 'administrative_units' },
      { key: this.getTranslatedUnitType('medical_clinics'), value: 'medical_clinics' },
      { key: this.getTranslatedUnitType('commercial_stores'), value: 'commercial_stores' },
      { key: this.getTranslatedUnitType('pharmacies'), value: 'pharmacies' },
      // Industrial
      { key: this.getTranslatedUnitType('warehouses'), value: 'warehouses' },
      { key: this.getTranslatedUnitType('factories'), value: 'factories' },
      // Lands
      { key: this.getTranslatedUnitType('residential_lands'), value: 'residential_villa_lands' },
      { key: this.getTranslatedUnitType('administrative_buildings'), value: 'administrative_lands' },
      { key: this.getTranslatedUnitType('residential_lands'), value: 'residential_lands' },
      { key: this.getTranslatedUnitType('commercial_lands'), value: 'commercial_administrative_lands' },
      { key: this.getTranslatedUnitType('medical_clinics'), value: 'medical_lands' },
      { key: this.getTranslatedUnitType('mixed_use_buildings'), value: 'mixed_lands' },
      { key: this.getTranslatedUnitType('warehouse_lands'), value: 'warehouses_land' },
      { key: this.getTranslatedUnitType('industrial_lands'), value: 'factory_lands' },
    ];
  }

  get insideCompoundUnitTypes() {
    return [
      // Residential
      { key: this.getTranslatedUnitType('apartments'), value: 'apartments' },
      { key: this.getTranslatedUnitType('duplexes'), value: 'duplexes' },
      { key: this.getTranslatedUnitType('studios'), value: 'studios' },
      { key: this.getTranslatedUnitType('penthouses'), value: 'penthouses' },
      { key: this.getTranslatedUnitType('i_villas'), value: 'i_villa' },
      // Villas
      { key: this.getTranslatedUnitType('standalone_villas'), value: 'standalone_villas' },
      { key: this.getTranslatedUnitType('town_houses'), value: 'town_houses' },
      { key: this.getTranslatedUnitType('twin_houses'), value: 'twin_houses' },
      // Commercial/Administrative
      { key: this.getTranslatedUnitType('administrative_units'), value: 'administrative_units' },
      { key: this.getTranslatedUnitType('medical_clinics'), value: 'medical_clinics' },
      { key: this.getTranslatedUnitType('commercial_stores'), value: 'commercial_stores' },
      { key: this.getTranslatedUnitType('pharmacies'), value: 'pharmacies' },
    ];
  }

  get villageUnitTypes() {
    return [
      // Residential
      { key: this.getTranslatedUnitType('apartments'), value: 'apartments' },
      { key: this.getTranslatedUnitType('duplexes'), value: 'duplexes' },
      { key: this.getTranslatedUnitType('studios'), value: 'studios' },
      { key: this.getTranslatedUnitType('penthouses'), value: 'penthouses' },
      { key: this.getTranslatedUnitType('basement'), value: 'basement' },
      { key: this.getTranslatedUnitType('roofs'), value: 'roofs' },
      // Villas
      { key: this.getTranslatedUnitType('i_villas'), value: 'i_villa' },
      { key: this.getTranslatedUnitType('twin_houses'), value: 'twin_houses' },
      { key: this.getTranslatedUnitType('town_houses'), value: 'town_houses' },
      { key: this.getTranslatedUnitType('standalone_villas'), value: 'standalone_villas' },
      { key: this.getTranslatedUnitType('villa'), value: 'villas' },
      { key: this.getTranslatedUnitType('full_buildings'), value: 'full_buildings' },
      // Commercial/Administrative
      { key: this.getTranslatedUnitType('administrative_units'), value: 'administrative_units' },
      { key: this.getTranslatedUnitType('medical_clinics'), value: 'medical_clinics' },
      { key: this.getTranslatedUnitType('commercial_stores'), value: 'commercial_stores' },
      { key: this.getTranslatedUnitType('pharmacies'), value: 'pharmacies' },
      // Other
      { key: this.getTranslatedUnitType('chalets'), value: 'chalets' },
      // Industrial
      { key: this.getTranslatedUnitType('warehouses'), value: 'warehouses' },
      { key: this.getTranslatedUnitType('factories'), value: 'factories' },
    ];
  }

  constructor(
    private unitService: UnitService,
    private route: ActivatedRoute,
    private cd: ChangeDetectorRef,
    public translationService: TranslationService,
    private propertyTranslationService: PropertyTranslationService
  ) {}

  // Helper method to get translated unit type
  getTranslatedUnitType(type: string): string {
    const currentLang = this.translationService.getCurrentLanguage() as 'en' | 'ar';
    return this.propertyTranslationService.translatePropertyType(type, currentLang, 50);
  }

  ngOnInit() {
    const userJson = localStorage.getItem('currentUser');
    this.user = userJson ? JSON.parse(userJson) : null;
    this.brokerId = this.user?.brokerId;

    // Initialize appliedFilters with brokerId
    this.appliedFilters = {
      brokerId: this.brokerId,
    };

    this.checkRouteParams();
    this.loadPropertiesCount();
  }

  loadPropertiesCount() {
    this.isLoading = true;
    this.unitService.getByBrokerId(this.brokerId).subscribe({
      next: (response: any) => {
        this.properties = response.data || [];

        console.log('ssssssssssss', response);
        this.isLoading = false;
        this.updateCardVisibility();
        this.cd.detectChanges();
      },
      error: (error) => {
        console.error('Error loading properties:', error);
        this.properties = [];
        this.isLoading = false;
        this.updateCardVisibility();
        this.cd.detectChanges();
      },
    });
  }

  onSearchTextChange(value: string): void {
    clearTimeout(this.searchTimeout);
    this.searchTimeout = setTimeout(() => {
      this.appliedFilters = {
        ...this.appliedFilters,
        unitType: value.trim(),
      };
      this.cd.detectChanges();
    }, 300);
  }

  toggleFilterDropdown() {
    this.isFilterDropdownVisible = !this.isFilterDropdownVisible;
  }

  onFiltersApplied(filters: any) {
    console.log('Received filters from unit-filter:', filters);
    this.toggleFilterDropdown();

    // Apply filters directly (brokerId is already included from unit-filter)
    this.appliedFilters = filters;

    // Update current filter values for dropdown consistency
    this.currentCompoundType = filters.compoundType || '';
    this.currentUnitType = filters.unitType || '';

    console.log('Applied filters to table:', this.appliedFilters);
    this.cd.detectChanges();
  }

  // Filter button methods
  // applyFilter(compoundType: string, unitType: string) {
  //   this.currentCompoundType = compoundType;
  //   this.currentUnitType = unitType;

  //   const filterKey = `${compoundType}_${unitType}`;

  //   // Update active filter buttons
  //   this.activeFilterButtons = [filterKey];

  //   // Apply filters to table
  //   this.appliedFilters = {
  //     ...this.appliedFilters,
  //     compoundType: compoundType,
  //     unitType: unitType,
  //     filterKey: filterKey
  //   };

  //   this.cd.detectChanges();
  // }

  clearFilters() {
    this.currentCompoundType = '';
    this.currentUnitType = '';
    this.activeFilterButtons = [];

    // Remove compound and property type filters but keep other filters
    const { compoundType, unitType, filterKey, ...otherFilters } =
      this.appliedFilters;
    this.appliedFilters = otherFilters;

    this.cd.detectChanges();
  }

  isFilterActive(compoundType: string, unitType: string): boolean {
    const filterKey = `${compoundType}_${unitType}`;
    return this.activeFilterButtons.includes(filterKey);
  }

  // Get fields to show based on compound type and unit type
  getFieldsToShow(): any[] {
    const compoundType = this.currentCompoundType;
    const type = this.currentUnitType;

    if (
      compoundType === 'outside_compound' &&
      (type === 'apartments' ||
        type === 'duplexes' ||
        type === 'studios' ||
        type === 'penthouses' ||
        type === 'roofs' ||
        type === 'basement')
    ) {
      return [
        'buildingNumber',
        'unitNumber',
        'floor',
        'unitArea',
        'numberOfRooms',
        'numberOfBathrooms',
        'unitFacing',
        'view',
        'finishingType',
        'deliveryStatus',
        'legalStatus',
        'otherAccessories',
        'paymentSystem',
        'pricePerMeterInCash',
        'totalPriceInCash',
        'pricePerMeterInInstallment',
        'totalPriceInInstallment',
      ];
    } else if (
      compoundType === 'outside_compound' &&
      (type == 'villas' || type == 'full_buildings')
    ) {
      return [
        'buildingNumber',
        'numberOfFloors',
        'buildingArea',
        'groundArea',
        'unitDescription',
        'unitDesign',
        'unitFacing',
        'view',
        'finishingType',
        'legalStatus',
        'otherAccessories',
        'paymentSystem',
        'pricePerMeterInCash',
        'totalPriceInCash',
        'pricePerMeterInInstallment',
        'totalPriceInInstallment',
      ];
    } else if (
      compoundType === 'outside_compound' &&
      (type === 'pharmacies' ||
        type === 'medical_clinics' ||
        type === 'administrative_units' ||
        type === 'commercial_stores')
    ) {
      return [
        'mallName',
        'buildingNumber',
        'unitNumber',
        'floor',
        'unitArea',
        'view',
        'finishingType',
        'fitOutCondition',
        'deliveryStatus',
        'activity',
        'financialStatus',
        'otherAccessories',
        'paymentSystem',
        'pricePerMeterInCash',
        'totalPriceInCash',
        'pricePerMeterInInstallment',
        'totalPriceInInstallment',
      ];
    } else if (
      compoundType === 'outside_compound' &&
      (type === 'warehouses' || type === 'factories')
    ) {
      return [
        'buildingNumber',
        'numberOfFloors',
        'groundArea',
        'buildingArea',
        'activity',
        'finishingType',
        'unitDescription',
        'legalStatus',
        'otherAccessories',
        'paymentSystem',
        'pricePerMeterInCash',
        'totalPriceInCash',
        'pricePerMeterInInstallment',
        'totalPriceInInstallment',
      ];
    } else if (
      compoundType === 'outside_compound' &&
      (type === 'residential_villa_lands' ||
        type === 'residential_lands' ||
        type === 'administrative_lands' ||
        type === 'commercial_administrative_lands' ||
        type === 'commercial_lands' ||
        type === 'medical_lands' ||
        type === 'mixed_lands' ||
        type === 'warehouses_land' ||
        type === 'industrial_lands')
    ) {
      return [
        'unitNumber',
        'groundArea',
        'fitOutCondition',
        'unitDescription',
        'buildingDeadline',
        'view',
        'legalStatus',
        'deliveryStatus',
        'financialStatus',
        'otherAccessories',
        'paymentSystem',
        'pricePerMeterInCash',
        'totalPriceInCash',
        'pricePerMeterInInstallment',
        'totalPriceInInstallment',
      ];
    } else if (
      compoundType === 'inside_compound' &&
      (type === 'apartments' ||
        type === 'duplexes' ||
        type === 'studios' ||
        type === 'penthouses' ||
        type === 'i_villa')
    ) {
      return [
        'compoundName',
        'buildingNumber',
        'unitNumber',
        'floor',
        'unitArea',
        'numberOfRooms',
        'numberOfBathrooms',
        'view',
        'finishingType',
        'deliveryStatus',
        'financialStatus',
        'otherAccessories',
        'requestedOver',
        'paymentSystem',
        'pricePerMeterInCash',
        'totalPriceInCash',
        'pricePerMeterInInstallment',
        'totalPriceInInstallment',
      ];
    } else if (
      compoundType === 'inside_compound' &&
      (type === 'standalone_villas' ||
        type === 'twin_houses' ||
        type === 'town_houses')
    ) {
      return [
        'compoundName',
        'buildingNumber',
        'numberOfFloors',
        'buildingArea',
        'groundArea',
        'numberOfRooms',
        'numberOfBathrooms',
        'view',
        'finishingType',
        'deliveryStatus',
        'financialStatus',
        'otherAccessories',
        'requestedOver',
        'paymentSystem',
        'pricePerMeterInCash',
        'totalPriceInCash',
        'pricePerMeterInInstallment',
        'totalPriceInInstallment',
      ];
    } else if (
      compoundType === 'inside_compound' &&
      (type === 'pharmacies' ||
        type === 'medical_clinics' ||
        type === 'administrative_units' ||
        type === 'commercial_stores')
    ) {
      return [
        'compoundName',
        'mallName',
        'buildingNumber',
        'unitNumber',
        'floor',
        'unitArea',
        'view',
        'finishingType',
        'deliveryStatus',
        'fitOutCondition',
        'financialStatus',
        'otherAccessories',
        'requestedOver',
        'paymentSystem',
        'pricePerMeterInCash',
        'totalPriceInCash',
        'pricePerMeterInInstallment',
        'totalPriceInInstallment',
      ];
    } else if (
      compoundType === 'village' &&
      (type === 'apartments' ||
        type === 'duplexes' ||
        type === 'studios' ||
        type === 'penthouses' ||
        type === 'basement' ||
        type === 'roofs' ||
        type === 'i_villa' ||
        type === 'villas' ||
        type === 'standalone_villas' ||
        type === 'full_buildings' ||
        type === 'twin_houses' ||
        type === 'town_houses' ||
        type === 'chalets')
    ) {
      return [
        'buildingNumber',
        'unitNumber',
        'unitArea',
        'floor',
        'view',
        'numberOfRooms',
        'numberOfBathrooms',
        'finishingType',
        'furnishingStatus',
        'otherAccessories',
        'rentRecurrence',
        'dailyRent',
        'monthlyRent',
        'annualRent',
      ];
    } else if (
      compoundType === 'village' &&
      (type === 'warehouses' ||
        type === 'factories' ||
        type === 'administrative_units' ||
        type === 'medical_clinics' ||
        type === 'commercial_stores' ||
        type === 'pharmacies')
    ) {
      return [
        'buildingNumber',
        'unitNumber',
        'unitArea',
        'floor',
        'view',
        'numberOfRooms',
        'numberOfBathrooms',
        'finishingType',
        'furnishingStatus',
        'otherAccessories',
        'rentRecurrence',
        'activity',
        'dailyRent',
        'monthlyRent',
      ];
    }

    return [];
  }

  // Check if a specific field should be shown
  shouldShowField(fieldName: string): boolean {
    return this.getFieldsToShow().includes(fieldName);
  }

  // Simple filter methods
  applyFilters(): void {
    // console.log('hiiiiiiiiiiiiiiiiiiiiiiiiii');
    this.appliedFilters = {
      ...this.appliedFilters,
      compoundType: this.currentCompoundType,
      unitType: this.currentUnitType,
    };

    this.cd.detectChanges();
  }

  canApplyFilters(): boolean {
    return !!(this.currentCompoundType && this.currentUnitType);
  }

  clearAllFilters(): void {
    this.currentCompoundType = '';
    this.currentUnitType = '';

    // Keep only brokerId and search filters
    const { compoundType, unitType, ...otherFilters } = this.appliedFilters;
    this.appliedFilters = otherFilters;

    this.cd.detectChanges();
  }

  // Get available unit types based on selected compound type
  getAvailableUnitTypes(): { key: string; value: string }[] {
    switch (this.currentCompoundType) {
      case 'outside_compound':
        return this.outsideCompoundUnitTypes;
      case 'inside_compound':
        return this.insideCompoundUnitTypes;
      case 'village':
        return this.villageUnitTypes;
      default:
        return [];
    }
  }

  // Handle compound type change
  onCompoundTypeChange(): void {
    // Reset unit type when compound type changes
    this.currentUnitType = '';
  }

  checkRouteParams() {
    this.route.queryParams.subscribe((params) => {
      if (params['success'] === 'add') {
        this.showSuccessCard = true;
        this.hideCardsAfterDelay();
      } else if (params['success'] === 'publish') {
        this.showPublishCard = true;
        this.hideCardsAfterDelay();
      }
    });
  }

  updateCardVisibility() {
    this.showEmptyCard =
      this.properties.length === 0 &&
      !this.showSuccessCard &&
      !this.showPublishCard;
  }

  hideCardsAfterDelay() {
    setTimeout(() => {
      this.showSuccessCard = false;
      this.showPublishCard = false;
      this.updateCardVisibility();
    }, 5000);
  }

  onBackToTable() {
    this.showSuccessCard = false;
    this.showPublishCard = false;
    this.updateCardVisibility();
    this.cd.detectChanges();
  }

  onFileSelected(event: any) {
    const file = event.target.files[0];
    if (file) {
      console.log('File selected:', file.name);
      this.handleFileUpload(file);
    }
  }

  handleFileUpload(file: File) {
    console.log('Uploading file:', file.name);
    this.unitService.uploadExcelUnits(file, this.brokerId).subscribe({
      next: async (response) => {
        console.log('Upload successful:', response);
        this.showEmptyCard = false;
        this.appliedFilters = {
          ...this.appliedFilters,
          refreshTimestamp: Date.now(),
        };
        this.loadPropertiesCount();
        this.cd.detectChanges();
        // Reset file input
        if (this.fileInput && this.fileInput.nativeElement) {
          this.fileInput.nativeElement.value = '';
        }
        this.showSuccessCard = true;
        this.hideCardsAfterDelay();
      },
      error: (error) => {
        console.error('Upload error:', error);
        Swal.fire('Error', 'Error uploading file. Please try again.', 'error');
      },
    });
  }

  openDownloadModal() {
    this.showDownloadModal = true;
  }

  downloadTemplate(data: { unitType: string; compoundType: string; operationType: string }) {
    this.showDownloadModal = false;
    console.log(data.unitType, data.compoundType, data.operationType);
    this.unitService.downloadExcelTemplate(data.unitType, data.compoundType, data.operationType).subscribe({
      next: (blob: Blob) => {
        saveAs(blob, 'units-template.xlsx');
      },
      error: (err) => console.error('Download error:', err),
    });
  }
}
