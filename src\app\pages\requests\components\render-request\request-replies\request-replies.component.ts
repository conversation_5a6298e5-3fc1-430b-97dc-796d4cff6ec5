import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { RequestService } from '../../../services/request.service';
import Swal from 'sweetalert2';
import { ActivatedRoute } from '@angular/router';
import { Modal } from 'bootstrap';
import { Page } from 'src/app/models/page.model';
import { environment } from 'src/environments/environment';
import * as bootstrap from 'bootstrap';
import { TranslationService } from 'src/app/modules/i18n';


@Component({
  selector: 'app-request-replies',
  templateUrl: './request-replies.component.html',
  styleUrl: './request-replies.component.scss'
})
export class RequestRepliesComponent implements OnInit {
  selectedBrokerId: any;
  requestId : any ;
  user : any ;

  page: Page = new Page();
  replies : any;

  dealClicked: boolean = false;
  orderBy: string = 'id';
  orderDir: string = 'desc';

  constructor(
    protected cd: ChangeDetectorRef,
    protected requestService: RequestService,
    private route :ActivatedRoute,
    public translationService: TranslationService
  ) {}

  ngOnInit() {
    let id = this.route.parent?.snapshot.paramMap.get('id') ?? null;
    this.requestId = id ? Number(id) : null;

    const userJson = localStorage.getItem('currentUser');
    this.user = userJson ? JSON.parse(userJson) : null;

    this.page.pageNumber = 0;
    this.page.size = environment.TABLE_LIMIT;

    this.getReplies();
  }


  getReplies()
  {
    this.requestService.getReplies(this.requestId, this.page).subscribe(
      (response:any) => {
        console.log(response.data);
        this.replies = response.data.data || response.data;
        this.page.totalElements = response.data.total || response.count || 0;
        this.page.count = Math.ceil(this.page.totalElements / this.page.size);
        this.cd.markForCheck();
      },
      (error: any) => {
        console.log(error);
        this.cd.markForCheck();
        Swal.fire('Failed to load data. please try again later.', '', 'error');
      }
    )
  }

  updateRequestStatus(requestId: number, userId: number, status: string, brokerId : number) {
    const payload = {
      userId,
      status,
    };

    this.selectedBrokerId = brokerId;

    this.requestService.updateRequestStatus(requestId, payload).subscribe({
      next: (response) => {
        this.dealClicked = true;
        this.cd.markForCheck();

        const modalElement = document.getElementById('ratingModal');
          if (modalElement) {
            const ratingModal = new bootstrap.Modal(modalElement);
            ratingModal.show();
          }
        },
      error: (error) => {
        console.error('Error updating status:', error);
      },
    });
  }

  formatAccessories(accessories: string[]): string {
    return accessories
      .map(item => item.replace(/_/g, ' '))
      .join(', ');
  }

  selectedUnitPlanImage: string | null = null;

  showUnitPlanModal(imgPath: string) {
      this.selectedUnitPlanImage = imgPath;

      const modalElement = document.getElementById('viewUnitPlanModal');
      if (modalElement) {
        const modal = new Modal(modalElement);
        modal.show();
      }
  }

  onPageChange(newPageNumber: number)
  {
    this.page.pageNumber = newPageNumber;
    this.getReplies();
  }

   sortData(column: string) {
     if (this.orderBy === column) {
      this.orderDir = this.orderDir === 'asc' ? 'desc' : 'asc';
    } else {
      this.orderBy = column;
      this.orderDir = 'asc';
    }

     this.page.orderBy = this.orderBy;
    this.page.orderDir = this.orderDir;
    this.page.pageNumber = 0;
    this.getReplies();
  }

   getSortArrow(column: string): string {
    if (this.orderBy !== column) {
      return '';
    }
    return this.orderDir === 'asc' ? '↑' : '↓';
  }

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'BROKER_NAME': 'اسم الوسيط',
        'UNIT_NUMBER': 'رقم الوحدة',
        'FLOOR': 'الطابق',
        'PROPERTY_NUMBER': 'رقم العقار',
        'AREA': 'المساحة',
        'ROOMS': 'الغرف',
        'BATHROOMS': 'الحمامات',
        'VIEW': 'الإطلالة',
        'DELIVERY_DATE': 'تاريخ التسليم',
        'FINISHING_STATUS': 'حالة التشطيب',
        'UNIT_PLAN': 'مخطط الوحدة',
        'UNIT_LOCATION_MASTER_PLAN': 'موقع الوحدة في المخطط الرئيسي',
        'PRICE': 'السعر',
        'PRICE_PER_METER_CASH': 'السعر للمتر نقداً',
        'PRICE_PER_METER_INSTALLMENT': 'السعر للمتر بالتقسيط',
        'TOTAL_PRICE_CASH': 'إجمالي السعر نقداً',
        'TOTAL_PRICE_INSTALLMENT': 'إجمالي السعر بالتقسيط',
        'OTHER_ACCESSORIES': 'الإكسسوارات الأخرى',
        'STATUS': 'الحالة',
        'CHAT': 'المحادثة',
        'FINISH_REQUEST': 'إنهاء الطلب',
        'ACTIONS': 'الإجراءات',
        'DEAL': 'صفقة',
        'CLOSE': 'إغلاق',
        'LOADING': 'جاري التحميل...',
        'NO_REPLIES_FOUND': 'لا توجد ردود',
        'VIEW_PLAN': 'عرض المخطط',
        'VIEW_LOCATION': 'عرض الموقع',
        'STATUS_SOLD': 'مباع',
        'STATUS_AVAILABLE': 'متاح',
        'STATUS_NEW': 'جديد',
        'STATUS_RESERVED': 'محجوز',
        'NO_REPLIES_AVAILABLE': 'لا توجد ردود متاحة',
        'NO_REPLIES_MESSAGE': 'لم يتم استلام أي ردود على هذا الطلب بعد. يرجى الرد على العميل بسرعة.',
        'FINISHING_ON_BRICK': 'على الطوب',
        'FINISHING_SEMI_FINISHED': 'نصف تشطيب',
        'FINISHING_COMPANY_FINISHED': 'تشطيب الشركة',
        'FINISHING_SUPER_LUX': 'سوبر لوكس',
        'FINISHING_ULTRA_SUPER_LUX': 'ألترا سوبر لوكس',
        'FINISHING_STANDARD': 'عادي'
      },
      'en': {
        'BROKER_NAME': 'Broker Name',
        'UNIT_NUMBER': 'Unit Number',
        'FLOOR': 'Floor',
        'PROPERTY_NUMBER': 'Property Number',
        'AREA': 'Area',
        'ROOMS': 'Rooms',
        'BATHROOMS': 'Bathrooms',
        'VIEW': 'View',
        'DELIVERY_DATE': 'Delivery Date',
        'FINISHING_STATUS': 'Finishing Status',
        'UNIT_PLAN': 'Unit Plan',
        'UNIT_LOCATION_MASTER_PLAN': 'Unit Location in Master Plan',
        'PRICE': 'Price',
        'PRICE_PER_METER_CASH': 'Price Per Meter in Cash',
        'PRICE_PER_METER_INSTALLMENT': 'Price Per Meter in Installment',
        'TOTAL_PRICE_CASH': 'Total Price Cash',
        'TOTAL_PRICE_INSTALLMENT': 'Total Price Installment',
        'OTHER_ACCESSORIES': 'Other Accessories',
        'STATUS': 'Status',
        'CHAT': 'Chat',
        'FINISH_REQUEST': 'Finish Request',
        'ACTIONS': 'Actions',
        'DEAL': 'Deal',
        'CLOSE': 'Close',
        'LOADING': 'Loading...',
        'NO_REPLIES_FOUND': 'No replies found',
        'VIEW_PLAN': 'View Plan',
        'VIEW_LOCATION': 'View Location',
        'STATUS_SOLD': 'Sold',
        'STATUS_AVAILABLE': 'Available',
        'STATUS_NEW': 'New',
        'STATUS_RESERVED': 'Reserved',
        'NO_REPLIES_AVAILABLE': 'No Replies Available',
        'NO_REPLIES_MESSAGE': 'No replies have been received for this request yet. Please respond to the client promptly.',
        'FINISHING_ON_BRICK': 'On Brick',
        'FINISHING_SEMI_FINISHED': 'Semi Finished',
        'FINISHING_COMPANY_FINISHED': 'Company Finished',
        'FINISHING_SUPER_LUX': 'Super Lux',
        'FINISHING_ULTRA_SUPER_LUX': 'Ultra Super Lux',
        'FINISHING_STANDARD': 'Standard'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }

}
