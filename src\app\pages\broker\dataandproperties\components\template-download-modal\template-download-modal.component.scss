// Simple Template Download Modal Styles
:host {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1055;
  display: flex;
  align-items: center;
  justify-content: center;
}

// Modal Backdrop
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1050;
}

// Modal Container
.modal {
  z-index: 1055;

  .modal-dialog {
    max-width: 500px;
    margin: 0 auto;
  }

  .modal-content {
    border-radius: 8px;
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }
}

// RTL Support
.rtl-layout {
  direction: rtl;
  text-align: right;

  .form-label {
    text-align: right;
    font-family: 'Noto <PERSON>', 'Tajawal', sans-serif;
  }

  .modal-title {
    font-family: 'Noto <PERSON> Arabic', 'Tajawal', sans-serif;
    font-weight: 600;
  }

  .btn {
    font-family: '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', sans-serif;
  }

  .form-select {
    text-align: right;
    direction: ltr;

    option {
      text-align: right;
      direction: rtl;
    }
  }

  // Fix for btn-close in RTL
  .modal-header {
    .btn-close {
      margin-left: auto !important;
      margin-right: 0 !important;
    }
  }
}

// Global RTL fix for btn-close
:host-context(html[dir="rtl"]) .modal-header .btn-close,
:host-context(html[lang="ar"]) .modal-header .btn-close {
  margin-left: 0 !important;
  margin-right: 0 !important;
}

// Responsive Design
@media (max-width: 768px) {
  .modal-dialog {
    max-width: 95%;
    margin: 1rem auto;
  }
}
