<div *ngIf="replies?.length > 0" [class.rtl-layout]="translationService.getCurrentLanguage() === 'ar'">
  <div class="table-responsive">
    <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3 mt-5">
      <thead>
        <tr class="fw-bold bg-light-dark-blue text-dark-blue me-1 ms-1"
          [class.rtl-table-header]="translationService.getCurrentLanguage() === 'ar'">
          <th class="w-25px rounded-start min-w-150px cursor-pointer"
            [class.ps-4]="translationService.getCurrentLanguage() !== 'ar'"
            [class.pe-4]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-th]="translationService.getCurrentLanguage() === 'ar'" (click)="sortData('broker_name')">
            {{ getTranslatedText('BROKER_NAME') }}
            <span class="text-primary fw-bold" [class.ms-1]="translationService.getCurrentLanguage() !== 'ar'"
              [class.me-1]="translationService.getCurrentLanguage() === 'ar'">{{ getSortArrow('broker_name') }}</span>
          </th>
          <th class="min-w-150px cursor-pointer" [class.rtl-th]="translationService.getCurrentLanguage() === 'ar'"
            (click)="sortData('unit_number')">
            {{ getTranslatedText('UNIT_NUMBER') }}
            <span class="text-primary fw-bold" [class.ms-1]="translationService.getCurrentLanguage() !== 'ar'"
              [class.me-1]="translationService.getCurrentLanguage() === 'ar'">{{ getSortArrow('unit_number') }}</span>
          </th>
          <th class="min-w-100px cursor-pointer" [class.rtl-th]="translationService.getCurrentLanguage() === 'ar'"
            (click)="sortData('floor')">
            {{ getTranslatedText('FLOOR') }}
            <span class="text-primary fw-bold" [class.ms-1]="translationService.getCurrentLanguage() !== 'ar'"
              [class.me-1]="translationService.getCurrentLanguage() === 'ar'">{{ getSortArrow('floor') }}</span>
          </th>
          <th class="min-w-150px cursor-pointer" [class.rtl-th]="translationService.getCurrentLanguage() === 'ar'"
            (click)="sortData('building_number')">
            {{ getTranslatedText('PROPERTY_NUMBER') }}
            <span class="text-primary fw-bold" [class.ms-1]="translationService.getCurrentLanguage() !== 'ar'"
              [class.me-1]="translationService.getCurrentLanguage() === 'ar'">{{ getSortArrow('building_number')
              }}</span>
          </th>
          <th class="min-w-100px cursor-pointer" [class.rtl-th]="translationService.getCurrentLanguage() === 'ar'"
            (click)="sortData('area')">
            {{ getTranslatedText('AREA') }}
            <span class="text-primary fw-bold" [class.ms-1]="translationService.getCurrentLanguage() !== 'ar'"
              [class.me-1]="translationService.getCurrentLanguage() === 'ar'">{{ getSortArrow('area') }}</span>
          </th>
          <th class="min-w-100px cursor-pointer" [class.rtl-th]="translationService.getCurrentLanguage() === 'ar'"
            (click)="sortData('rooms_count')">
            {{ getTranslatedText('ROOMS') }}
            <span class="text-primary fw-bold" [class.ms-1]="translationService.getCurrentLanguage() !== 'ar'"
              [class.me-1]="translationService.getCurrentLanguage() === 'ar'">{{ getSortArrow('rooms_count') }}</span>
          </th>
          <th class="min-w-150px cursor-pointer" [class.rtl-th]="translationService.getCurrentLanguage() === 'ar'"
            (click)="sortData('bathrooms_count')">
            {{ getTranslatedText('BATHROOMS') }}
            <span class="text-primary fw-bold" [class.ms-1]="translationService.getCurrentLanguage() !== 'ar'"
              [class.me-1]="translationService.getCurrentLanguage() === 'ar'">{{ getSortArrow('bathrooms_count')
              }}</span>
          </th>
          <th class="min-w-150px cursor-pointer" [class.rtl-th]="translationService.getCurrentLanguage() === 'ar'"
            (click)="sortData('view')">
            {{ getTranslatedText('VIEW') }}
            <span class="text-primary fw-bold" [class.ms-1]="translationService.getCurrentLanguage() !== 'ar'"
              [class.me-1]="translationService.getCurrentLanguage() === 'ar'">{{ getSortArrow('view') }}</span>
          </th>
          <th class="min-w-150px cursor-pointer" [class.rtl-th]="translationService.getCurrentLanguage() === 'ar'"
            (click)="sortData('delivery_date')">
            {{ getTranslatedText('DELIVERY_DATE') }}
            <span class="text-primary fw-bold" [class.ms-1]="translationService.getCurrentLanguage() !== 'ar'"
              [class.me-1]="translationService.getCurrentLanguage() === 'ar'">{{ getSortArrow('delivery_date') }}</span>
          </th>
          <th class="min-w-150px cursor-pointer" [class.rtl-th]="translationService.getCurrentLanguage() === 'ar'"
            (click)="sortData('finishing_status')">
            {{ getTranslatedText('FINISHING_STATUS') }}
            <span class="text-primary fw-bold" [class.ms-1]="translationService.getCurrentLanguage() !== 'ar'"
              [class.me-1]="translationService.getCurrentLanguage() === 'ar'">{{ getSortArrow('finishing_status')
              }}</span>
          </th>
          <th class="min-w-150px" [class.rtl-th]="translationService.getCurrentLanguage() === 'ar'">
            {{ getTranslatedText('UNIT_PLAN') }}
          </th>
          <th class="min-w-250px" [class.rtl-th]="translationService.getCurrentLanguage() === 'ar'">
            {{ getTranslatedText('UNIT_LOCATION_MASTER_PLAN') }}
          </th>
          <th class="min-w-200px cursor-pointer" [class.rtl-th]="translationService.getCurrentLanguage() === 'ar'"
            (click)="sortData('price_per_meter_cash')">
            {{ getTranslatedText('PRICE_PER_METER_CASH') }}
            <span class="text-primary fw-bold" [class.ms-1]="translationService.getCurrentLanguage() !== 'ar'"
              [class.me-1]="translationService.getCurrentLanguage() === 'ar'">{{ getSortArrow('price_per_meter_cash')
              }}</span>
          </th>
          <th class="min-w-250px cursor-pointer" [class.rtl-th]="translationService.getCurrentLanguage() === 'ar'"
            (click)="sortData('price_per_meter_installment')">
            {{ getTranslatedText('PRICE_PER_METER_INSTALLMENT') }}
            <span class="text-primary fw-bold" [class.ms-1]="translationService.getCurrentLanguage() !== 'ar'"
              [class.me-1]="translationService.getCurrentLanguage() === 'ar'">{{
              getSortArrow('price_per_meter_installment') }}</span>
          </th>
          <th class="min-w-150px cursor-pointer" [class.rtl-th]="translationService.getCurrentLanguage() === 'ar'"
            (click)="sortData('total_price_cash')">
            {{ getTranslatedText('TOTAL_PRICE_CASH') }}
            <span class="text-primary fw-bold" [class.ms-1]="translationService.getCurrentLanguage() !== 'ar'"
              [class.me-1]="translationService.getCurrentLanguage() === 'ar'">{{ getSortArrow('total_price_cash')
              }}</span>
          </th>
          <th class="min-w-200px cursor-pointer" [class.rtl-th]="translationService.getCurrentLanguage() === 'ar'"
            (click)="sortData('total_price_installment')">
            {{ getTranslatedText('TOTAL_PRICE_INSTALLMENT') }}
            <span class="text-primary fw-bold" [class.ms-1]="translationService.getCurrentLanguage() !== 'ar'"
              [class.me-1]="translationService.getCurrentLanguage() === 'ar'">{{ getSortArrow('total_price_installment')
              }}</span>
          </th>
          <th class="min-w-150px cursor-pointer" [class.rtl-th]="translationService.getCurrentLanguage() === 'ar'"
            (click)="sortData('other_accessories')">
            {{ getTranslatedText('OTHER_ACCESSORIES') }}
            <span class="text-primary fw-bold" [class.ms-1]="translationService.getCurrentLanguage() !== 'ar'"
              [class.me-1]="translationService.getCurrentLanguage() === 'ar'">{{ getSortArrow('other_accessories')
              }}</span>
          </th>
          <th class="min-w-50px rounded-end cursor-pointer"
            [class.text-end]="translationService.getCurrentLanguage() !== 'ar'"
            [class.text-start]="translationService.getCurrentLanguage() === 'ar'"
            [class.pe-4]="translationService.getCurrentLanguage() !== 'ar'"
            [class.ps-4]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-th]="translationService.getCurrentLanguage() === 'ar'" (click)="sortData('status')">
            {{ getTranslatedText('STATUS') }}
            <span class="text-primary fw-bold" [class.ms-1]="translationService.getCurrentLanguage() !== 'ar'"
              [class.me-1]="translationService.getCurrentLanguage() === 'ar'">{{ getSortArrow('status') }}</span>
          </th>
          <th class="min-w-50px rounded-end cursor-pointer"
            [class.text-end]="translationService.getCurrentLanguage() !== 'ar'"
            [class.text-start]="translationService.getCurrentLanguage() === 'ar'"
            [class.pe-4]="translationService.getCurrentLanguage() !== 'ar'"
            [class.ps-4]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-th]="translationService.getCurrentLanguage() === 'ar'">
            {{ getTranslatedText('CHAT') }}
          </th>
          <th class="min-w-100px rounded-end" [class.text-end]="translationService.getCurrentLanguage() !== 'ar'"
            [class.text-start]="translationService.getCurrentLanguage() === 'ar'"
            [class.pe-4]="translationService.getCurrentLanguage() !== 'ar'"
            [class.ps-4]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-th]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('FINISH_REQUEST') }}
          </th>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngFor="let reply of replies">
          <ng-container *ngFor="let unit of reply.units">
            <tr>
              <td class="ps-4">
                <span class="text-gray-900 fw-bold fs-5">{{ reply.brokerName }}</span>
              </td>
              <td>
                <span class="text-gray-900 fw-bold fs-5">{{ unit.unitNumber }}</span>
              </td>
              <td>
                <span class="text-gray-900 fw-bold fs-5">{{ unit.floor }}</span>
              </td>
              <td>
                <span class="text-gray-900 fw-bold fs-5"> {{ unit.buildingNumber }}</span>
              </td>
              <td>
                <span class="text-gray-900 fw-bold fs-5">{{ unit.unitArea | number:'1.0-2' }} m²</span>
              </td>
              <td>
                <span class="text-gray-900 fw-bold fs-5">{{ unit.numberOfRooms }}</span>
              </td>
              <td>
                <span class="text-gray-900 fw-bold fs-5">{{ unit.numberOfBathrooms }}</span>
              </td>
              <td>
                <span class="text-gray-900 fw-bold fs-5">{{ unit.view }}</span>
              </td>
              <td>
                <span class="text-gray-900 fw-bold fs-5">{{ unit.deliveryDate }}</span>
              </td>
              <td>
                <span *ngIf="unit.finishingType === 'On Brick'" class="badge badge-light-danger fs-5 p-3">
                  {{ getTranslatedText('FINISHING_ON_BRICK') }}
                </span>
                <span *ngIf="unit.finishingType === 'Semi finished'" class="badge badge-light-danger fs-5 p-3">
                  {{ getTranslatedText('FINISHING_SEMI_FINISHED') }}
                </span>
                <span *ngIf="unit.finishingType === 'Company finished'" class="badge badge-light-success fs-5 p-3">
                  {{ getTranslatedText('FINISHING_COMPANY_FINISHED') }}
                </span>
                <span *ngIf="unit.finishingType === 'Super Lux'" class="badge badge-light-info fs-5 p-3">
                  {{ getTranslatedText('FINISHING_SUPER_LUX') }}
                </span>
                <span *ngIf="unit.finishingType === 'Ultra Super Lux'" class="badge badge-light-info fs-5 p-3">
                  {{ getTranslatedText('FINISHING_ULTRA_SUPER_LUX') }}
                </span>
                <span *ngIf="unit.finishingType === 'Standard'" class="badge badge-light-info fs-5">
                  {{ getTranslatedText('FINISHING_STANDARD') }}
                </span>
              </td>
              <td>
                <button class="btn btn-sm btn-light-info p-3" (click)="showUnitPlanModal(unit.diagram)">
                  <i class="fa-solid fa-file-image me-1"></i> {{ getTranslatedText('VIEW_PLAN') }}
                </button>
              </td>
              <td>
                <button class="btn btn-sm btn-light-info p-3" (click)="showUnitPlanModal(unit.locationInMasterPlan)">
                  <i class="fa-solid fa-file-image me-1"></i> {{ getTranslatedText('VIEW_LOCATION') }}
                </button>
              </td>
              <td>
                <span class="badge badge-light-warning fw-bold fs-5 p-3">{{ unit.pricePerMeterInCash }} EGP</span>
              </td>
              <td>
                <span class="badge badge-light-primary fw-bold fs-5 p-3">{{ unit.pricePerMeterInInstallment }}
                  EGP</span>
              </td>
              <td>
                <span class="badge badge-light-warning fw-bold fs-5 p-3">{{ unit.totalPriceInCash }} EGP</span>
              </td>
              <td>
                <span class="badge badge-light-primary fw-bold fs-5 p-3">{{ unit.totalPriceInInstallment }} EGP</span>
              </td>
              <td>
                <span class="text-gray-900 fw-bold fs-5">{{ formatAccessories(unit.otherAccessories) }}</span>
              </td>
              <td class="text-end fs-5 pe-4">
                <span *ngIf="unit.status === 'sold'" class="badge badge-light-danger fw-bold fs-5">{{
                  getTranslatedText('STATUS_SOLD') }}</span>
                <span *ngIf="unit.status === 'available'" class="badge badge-light-warning fw-bold fs-5">{{
                  getTranslatedText('STATUS_AVAILABLE') }}</span>
                <span *ngIf="unit.status === 'new'" class="badge badge-light-success fw-bold fs-5 p-3">{{
                  getTranslatedText('STATUS_NEW') }}</span>
                <span *ngIf="unit.status === 'reserved'" class="badge badge-light-info fw-bold fs-5 p-3">{{
                  getTranslatedText('STATUS_RESERVED') }}</span>
              </td>
              <td>
                <a class="d-flex align-items-center  me-5 mb-2 mf-2" [routerLink]="['/chat']"
                  [queryParams]="{ chatWithUID: reply.userId }">
                  <i class="fa-regular fa-comment-dots me-1 text-success text-hover-success fs-5"></i>
                </a>
              </td>
              <td>
                <div class="menu-item px-3" *ngIf="user?.role == 'client' && !dealClicked">
                  <a class="btn btn-sm btn-mid-blue me-3 cursor-pointer fw-bold fs-6"
                    (click)="updateRequestStatus(requestId, user?.id, 'finished' , reply.brokerId)">
                    {{ getTranslatedText('DEAL') }}
                  </a>
                </div>
              </td>
            </tr>
          </ng-container>
        </ng-container>
      </tbody>
    </table>
  </div>

  <div class="m-2">
    <app-pagination [totalItems]="page.totalElements" [itemsPerPage]="page.limit" [currentPage]="page.pageNumber"
      (pageChange)="onPageChange($event)">
    </app-pagination>
  </div>

  <!-- Unit Plan Modal -->
  <div class="modal fade" id="viewUnitPlanModal" tabindex="-1" aria-labelledby="viewUnitPlanModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="viewUnitPlanModalLabel">{{ getTranslatedText('UNIT_PLAN') }}</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body text-center">
          <img *ngIf="selectedUnitPlanImage" [src]="selectedUnitPlanImage" class="img-fluid" alt="Unit Plan">
        </div>
      </div>
    </div>
  </div>
</div>



<div *ngIf="replies?.length == 0">
  <div class="row mb-5">
    <div class="col-md-5">
      <div class="d-flex align-items-center bg-light-dark-blue rounded p-5" role="alert" aria-live="polite">
        <span class="svg-icon text-info me-5" aria-hidden="true">
          <i class="fas fa-exclamation-circle ms-1 fs-5 text-dark-blue"></i>
        </span>
        <div class="flex-grow-1 me-2">
          <span class="fw-bolder text-dark-blue fs-6">
            {{ getTranslatedText('NO_REPLIES_AVAILABLE') }}
          </span>
          <span class="text-muted fw-bold d-block">
            {{ getTranslatedText('NO_REPLIES_MESSAGE') }}
          </span>
        </div>
      </div>
    </div>
    <div class="col-md-7"></div>
  </div>
</div>

<div class="modal fade" id="ratingModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content p-4">
      <app-rating [brokerId]="selectedBrokerId"></app-rating>
    </div>
  </div>
</div>